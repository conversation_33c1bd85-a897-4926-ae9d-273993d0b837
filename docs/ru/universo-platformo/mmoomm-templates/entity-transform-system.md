# Система Transform для сущностей

Система Transform для сущностей в шаблонах MMOOMM обеспечивает точное управление позиционированием, поворотом и масштабированием сущностей в 3D пространстве. Эта система гарантирует, что данные Transform, настроенные в интерфейсе Chatflow, точно применяются к сгенерированным сущностям PlayCanvas.

## Обзор

Система Transform обрабатывает данные трансформации сущностей через полный конвейер:

1. **Интерфейс Chatflow** - Пользователь настраивает свойства Transform
2. **UPDLProcessor** - Обрабатывает и проверяет данные Transform
3. **EntityHandler** - Применяет Transform к сущностям PlayCanvas
4. **Типы сущностей** - Соблюдают настройки Transform без переопределения

## Свойства Transform

### Позиция
- **Тип**: Vector3 (x, y, z)
- **По умолчанию**: (0, 0, 0)
- **Единицы**: Мировые единицы
- **Пример**: `{ x: -10, y: 0, z: 5 }`

### Поворот
- **Тип**: Vector3 (x, y, z)
- **По умолчанию**: (0, 0, 0)
- **Единицы**: Градусы (углы Эйлера)
- **Пример**: `{ x: 0, y: 45, z: 0 }`

### Масштаб
- **Тип**: Vector3 (x, y, z)
- **По умолчанию**: (1, 1, 1)
- **Единицы**: Множитель
- **Пример**: `{ x: 4, y: 4, z: 4 }`

## Конвейер обработки данных

### 1. Конфигурация Chatflow

Пользователи настраивают свойства Transform в интерфейсе Chatflow:

```json
{
  "transform": {
    "pos": [-10, 0, 5],
    "rot": [0, 0, 0],
    "scale": [4, 4, 4]
  }
}
```

### 2. Обработка UPDLProcessor

UPDLProcessor преобразует формат массива в формат объекта:

```typescript
// Вход: [4, 4, 4]
// Выход: { x: 4, y: 4, z: 4 }
transform.scale = Array.isArray(sc)
  ? { x: Number(sc[0]) || 1, y: Number(sc[1]) || 1, z: Number(sc[2]) || 1 }
  : sc
```

### 3. Применение EntityHandler

EntityHandler применяет данные Transform к сущностям PlayCanvas:

```typescript
const transform = entity.data?.transform || {}
const position = transform.position || { x: 0, y: 0, z: 0 }
const rotation = transform.rotation || { x: 0, y: 0, z: 0 }
const scale = transform.scale || { x: 1, y: 1, z: 1 }

entity.setLocalPosition(position.x, position.y, position.z);
entity.setLocalEulerAngles(rotation.x, rotation.y, rotation.z);
entity.setLocalScale(scale.x, scale.y, scale.z);
```

### 4. Соблюдение типами сущностей

Типы сущностей соблюдают настройки Transform без переопределения:

```typescript
// Тип сущности астероид соблюдает заданный пользователем масштаб
// Нет генерации случайного масштаба при указанном Transform
const entityScale = entity.getLocalScale();
const scaleMultiplier = Math.max(entityScale.x, entityScale.y, entityScale.z);
```

## Примеры конфигурации

### Базовое позиционирование сущности

```json
{
  "entityType": "ship",
  "transform": {
    "pos": [0, 2, 0],
    "rot": [0, 0, 0],
    "scale": [1, 1, 1]
  }
}
```

### Конфигурация большого астероида

```json
{
  "entityType": "asteroid",
  "transform": {
    "pos": [-10, 0, 5],
    "rot": [0, 0, 0],
    "scale": [4, 4, 4]
  }
}
```

### Размещение торговой станции

```json
{
  "entityType": "station",
  "transform": {
    "pos": [20, 0, 0],
    "rot": [0, 45, 0],
    "scale": [2, 2, 2]
  }
}
```

## Лучшие практики

### Соображения масштаба

1. **Явные значения**: Всегда указывайте точные значения масштаба для предсказуемых результатов
2. **Пропорциональное масштабирование**: Используйте равномерное масштабирование (например, 4,4,4) для согласованных пропорций
3. **Добыча ресурсов**: Большие астероиды (больший масштаб) автоматически дают больше ресурсов

### Рекомендации по позиционированию

1. **Мировые координаты**: Используйте систему мировых координат для позиционирования
2. **Избежание коллизий**: Обеспечьте достаточное расстояние между сущностями
3. **Игровой баланс**: Учитывайте дальность взаимодействия при позиционировании сущностей

### Лучшие практики поворота

1. **Углы Эйлера**: Используйте градусы для интуитивного указания поворота
2. **Блокировка карданного подвеса**: Учитывайте возможную блокировку при поворотах на 90 градусов
3. **Визуальная ориентация**: Учитывайте визуальную ориентацию для игровых элементов

## Устранение неполадок

### Распространенные проблемы

**Масштаб не применяется**
- Проверьте правильность форматирования данных Transform в Chatflow
- Убедитесь, что тип сущности не переопределяет значения масштаба
- Проверьте корректность передачи данных масштаба в UPDLProcessor

**Смещение позиции**
- Подтвердите понимание системы координат (Y вверх в PlayCanvas)
- Проверьте конфликты логики позиционирования типа сущности
- Проверьте парсинг данных Transform в UPDLProcessor

**Проблемы поворота**
- Используйте углы Эйлера в градусах, а не радианах
- Учитывайте порядок поворота (XYZ) для сложных поворотов
- Тестируйте с простыми поворотами по одной оси

### Отладочная информация

Включите отладочное логирование для отслеживания потока данных Transform:

```typescript
console.log('[DEBUG] Entity transform data:', {
  entityId,
  transform,
  position,
  rotation,
  scale
});
```

## Техническая реализация

### Расположение файлов

- **UPDLProcessor**: `src/builders/common/UPDLProcessor.ts`
- **EntityHandler**: `src/builders/templates/mmoomm/playcanvas/handlers/EntityHandler/index.ts`
- **Типы сущностей**: `src/builders/templates/mmoomm/playcanvas/handlers/EntityHandler/entityTypes/`

### Ключевые функции

- `UPDLProcessor.processEntityNodes()` - Обработка данных Transform
- `EntityHandler.attach()` - Применение Transform
- `generateEntityTypeLogic()` - Обработка Transform для конкретных типов сущностей

## Связанные системы

- [Система компонентов](component-system.md) - Конфигурация сущностей на основе компонентов
- [Торговая система](trading-system.md) - Дальность взаимодействия торговых компонентов
- [Система ресурсов](resource-system.md) - Расчет добычи ресурсов на основе масштаба
