# Important! Grave! Важно!

-   Workers of the world, unite!
-   <PERSON><PERSON><PERSON><PERSON> el ĉiuj landoj, unuiĝu!
-   Пролетарии всех стран, соединяйтесь!

# Universo Platformo React

[![Version](https://img.shields.io/badge/version-0.23.0--alpha-blue)](https://github.com/teknokomo/universo-platformo-react)
[![License: Omsk Open License](https://img.shields.io/badge/license-Omsk%20Open%20License-green)](LICENSE.md)
[![License: Apache 2.0](https://img.shields.io/badge/license-Apache%202.0-blue)](LICENSE-Flowise.md)

## Basic Information

![image](https://github.com/user-attachments/assets/0be3687e-afe2-46be-a0f6-487ff03d9c80)

Implementation of Universo Platformo / Universo MMOOMM / Universo Kiberplano built on React and related stack. This project is based on [Flowise AI](https://github.com/FlowiseAI/Flowise) (version 2.2.8) with multi-user functionality through Supabase integration and extended with UPDL (Universal Platform Description Language) for creating 3D/AR/VR applications.

**In this repository, public efforts are currently underway to create Universo Platformo / Universo MMOOMM, in order to launch a global teknokomization and save humanity from final enslavement and complete destruction by creating special mass multi-user virtual worlds, such as Universo MMOOMM, and a platform for their creation - Universo Platformo, initially with gaming functionality, and then with the addition of the Cyberplan functionality.**

Universo Platformo React serves as the foundation for implementing **Universo Kiberplano** - a global planning and implementation system that unifies plans, tasks, and resources while controlling robots. This system aims to create a comprehensive framework for worldwide coordination of efforts, optimizing resource allocation, and enabling efficient automation through robotic systems, all within a unified digital [TemplateRegistry] Initializing with available templates
index-BrrUjL9A.js:5074 [TemplateRegistry] Registered template: quiz (Квиз-приложение)
index-BrrUjL9A.js:4088 [MMOOMM Scripts] Simple script system ready
index-BrrUjL9A.js:4136 [BuilderSystemsManager] Initializing all systems...
index-BrrUjL9A.js:4136 [BuilderSystemsManager] All systems initialized successfully
index-BrrUjL9A.js:5072 [PlayCanvasMMOOMMBuilder] Modular systems initialized
index-BrrUjL9A.js:5074 [TemplateRegistry] Registered template: mmoomm (playcanvasTemplates.mmoomm.name)
index-BrrUjL9A.js:5074 [TemplateRegistry] Registered 2 templates
PublicFlowView-BzMnlAdh.js:1 🔍 [PublicFlowView] Loading public flow for ID: 63285597-89aa-4264-87bc-0bd5c45b6307
PublicFlowView-BzMnlAdh.js:1 📡 [PublicFlowView] Chatflow data received: {id: '63285597-89aa-4264-87bc-0bd5c45b6307', name: 'Universo MMOOMM', hasFlowData: true, hasChatbotConfig: true, isPublic: true}
PublicFlowView-BzMnlAdh.js:1 🔧 [PublicFlowView] Parsed chatbotConfig: {playcanvas: {…}}
PublicFlowView-BzMnlAdh.js:1 🎯 [PublicFlowView] Active technology found: playcanvas
PublicFlowView-BzMnlAdh.js:1 🚀 [PublicFlowView] Rendering technology: playcanvas
PublicFlowView-BzMnlAdh.js:1 🎮 [PlayCanvasViewPage] URL params: {flowId: '63285597-89aa-4264-87bc-0bd5c45b6307', id: undefined, publicationId: '63285597-89aa-4264-87bc-0bd5c45b6307'}
PublicFlowView-BzMnlAdh.js:1 🎮 [PlayCanvasViewPage] Props: {hasFlowData: true, hasConfig: true}
PublicFlowView-BzMnlAdh.js:1 🎮 [PlayCanvasViewPage] Loading PlayCanvas space
PublicFlowView-BzMnlAdh.js:1 🎮 [PlayCanvasViewPage] Using provided props data
PublicFlowView-BzMnlAdh.js:1 🔧 [PlayCanvasViewPage] Processing flow data with UPDLProcessor
index-BrrUjL9A.js:5074 [UPDLProcessor] Processing flow with 16 nodes and 15 edges
index-BrrUjL9A.js:5074 [UPDLProcessor] Single space detected, using legacy processing
index-BrrUjL9A.js:5074 [UPDLProcessor] Single space built: 4 entities, 0 objects
PublicFlowView-BzMnlAdh.js:1 🔧 [PlayCanvasViewPage] UPDLProcessor result: {hasUpdlSpace: true, hasMultiScene: false}
PublicFlowView-BzMnlAdh.js:1 🏗️ [PlayCanvasViewPage] Generating HTML with TemplateRegistry
index-BrrUjL9A.js:4088 [MMOOMM Scripts] Simple script system ready
index-BrrUjL9A.js:4136 [BuilderSystemsManager] Initializing all systems...
index-BrrUjL9A.js:4136 [BuilderSystemsManager] All systems initialized successfully
index-BrrUjL9A.js:5072 [PlayCanvasMMOOMMBuilder] Modular systems initialized
index-BrrUjL9A.js:5072 [PlayCanvasMMOOMMBuilder] Building MMOOMM project with modular systems
index-BrrUjL9A.js:5072 [PlayCanvasMMOOMMBuilder] Building single scene MMOOMM
index-BrrUjL9A.js:414 [AbstractTemplateBuilder] extractNodes called with: {hasUpdlSpace: true, hasFlowData: false, hasMultiScene: false, updlSpaceObjectCount: 0}
index-BrrUjL9A.js:414 [AbstractTemplateBuilder] extractNodes result from updlSpace: {spacesCount: 1, objectsCount: 0, camerasCount: 0, lightsCount: 0, dataCount: 2}
index-BrrUjL9A.js:414 [AbstractTemplateBuilder] extractNodes called with: {hasUpdlSpace: true, hasFlowData: false, hasMultiScene: false, updlSpaceObjectCount: 0}
index-BrrUjL9A.js:414 [AbstractTemplateBuilder] extractNodes result from updlSpace: {spacesCount: 1, objectsCount: 0, camerasCount: 0, lightsCount: 0, dataCount: 2}
index-BrrUjL9A.js:5072 [PlayCanvasMMOOMMBuilder] Processing nodes - Entities: 4, Components: 9
index-BrrUjL9A.js:4136 [BuilderSystemsManager] Generating complete HTML document...
index-BrrUjL9A.js:4144 [BuilderSystemsManager] Generating embedded JavaScript...
index-BrrUjL9A.js:4151 [BuilderSystemsManager] Embedded JavaScript generation complete
index-BrrUjL9A.js:4144 [BuilderSystemsManager] HTML document generation complete
PublicFlowView-BzMnlAdh.js:1 🏗️ [PlayCanvasViewPage] HTML generation completed: {htmlLength: 117934, hasHTML: true}
PublicFlowView-BzMnlAdh.js:1 🖼️ [PlayCanvasViewPage] Rendering HTML in iframe with srcDoc
PublicFlowView-BzMnlAdh.js:1 📝 [PlayCanvasViewPage] HTML set via srcDoc
VM101 about:srcdoc:15 [MMOOMM] Enabling physics system...
VM101 about:srcdoc:25 [MMOOMM] Physics system enabled with zero gravity for space
VM101 about:srcdoc:43 [MMOOMM] Canvas configuration complete
VM101 about:srcdoc:59 [MMOOMM] Lighting setup complete
VM101 about:srcdoc:78 [MMOOMM] Camera setup complete
VM101 about:srcdoc:88 [MMOOMM] PlayCanvas application initialized successfully
VM101 about:srcdoc:705 [MMOOMM] Setting up app start event listener
VM101 about:srcdoc:756 [MMOOMM] Virtual world initialized - ready for players
VM101 about:srcdoc:775 [MMO Space] Initializing root space: default-space
VM101 about:srcdoc:814 [MMO Space] Environment setup complete for default-space
VM101 about:srcdoc:820 [MMO Space] Network setup ready for Universo gateway
VM101 about:srcdoc:992 [Ship] Created ship entity with position: [0, 2, 0]
VM101 about:srcdoc:993 [Ship] Initial rotation set to: [0, 0, 0]
VM101 about:srcdoc:1257 [Camera] Initialized at position: [0, 11.6, -21.599999999999998]
VM101 about:srcdoc:1532 [Ship] Camera controller initialized for ship
VM101 about:srcdoc:1280 [LaserSystem] Initializing laser system
VM101 about:srcdoc:1281 [LaserSystem] Initialization complete
VM101 about:srcdoc:1556 [Entity] Created ship entity: {id: 'Entity_0', position: '[0, 2, 0]', enabled: true, hasModel: true, hasRigidbody: true, …}
VM101 about:srcdoc:1768 [Entity] Created station entity: {id: 'Entity_1', position: '[20, 0, 0]', enabled: true, hasModel: true, hasRigidbody: false, …}
VM101 about:srcdoc:1878 Uncaught ReferenceError: scale is not defined
    at VM101 about:srcdoc:1878:19
    at VM101 about:srcdoc:2008:3
(anonymous) @ VM101 about:srcdoc:1878
(anonymous) @ VM101 about:srcdoc:2008
PublicFlowView-BzMnlAdh.js:1 ✅ [PlayCanvasViewPage] Iframe loaded successfully
VM101 about:srcdoc:29 [MMOOMM] Physics world state: {enabled: true, hasWorld: false, gravity: '[0, 0, 0]'}
VM101 about:srcdoc:740 [MMOOMM] Timeout initialization attempt
VM101 about:srcdoc:749 [MMOOMM] Calling initializeSpaceControls from timeout
VM101 about:srcdoc:635 [Space] Initializing SpaceControls...
VM101 about:srcdoc:643 [Space] SpaceControls found, calling init()
VM101 about:srcdoc:263 [SpaceControls] Initializing input handling...
VM101 about:srcdoc:405 === SPACE SHIP CONTROLS ===
VM101 about:srcdoc:406 WASD - Move Forward/Back/Turn Left/Right
VM101 about:srcdoc:407 Shift+WASD - Strafe Movement
VM101 about:srcdoc:408 Q/Z - Move Up/Down
VM101 about:srcdoc:409 E/C - Roll Left/Right
VM101 about:srcdoc:410 Arrow Keys - Pitch Up/Down
VM101 about:srcdoc:411 Space - Fire Laser (Mining)
VM101 about:srcdoc:412 F - Interact (Trading)
VM101 about:srcdoc:413 Mouse Wheel - Camera Zoom
VM101 about:srcdoc:414 ===========================
VM101 about:srcdoc:645 [Space] SpaceControls.init() completed
VM101 about:srcdoc:648 [Space] Looking for player ship...
VM101 about:srcdoc:651 [Space] Total entities found: 2
VM101 about:srcdoc:654 [Space] Entity Entity_0 components: {hasModel: true, hasShipController: true, hasRigidbody: true, position: '[0, 2, 0]', visible: true}
VM101 about:srcdoc:664 [Space] Player ship found: Entity_0
VM101 about:srcdoc:654 [Space] Entity Entity_1 components: {hasModel: true, hasShipController: false, hasRigidbody: false, position: '[20, 0, 0]', visible: true}
VM101 about:srcdoc:672 [Space] Player ship successfully assigned at position: [0, 2, 0]
VM101 about:srcdoc:500 [Space] Initializing physics for all entities...
VM101 about:srcdoc:519 [Space] Physics system state: {enabled: true, gravity: e, hasWorld: false}
VM101 about:srcdoc:546 [Space] Entity Entity_0 has rigidbody component but no physics body
(anonymous) @ VM101 about:srcdoc:546
initializePhysics @ VM101 about:srcdoc:529
(anonymous) @ VM101 about:srcdoc:678
setTimeout
initializeSpaceControls @ VM101 about:srcdoc:676
(anonymous) @ VM101 about:srcdoc:750
setTimeout
(anonymous) @ VM101 about:srcdoc:739
VM101 about:srcdoc:553 [Space] Physics initialized for entity: Entity_0 {type: 'dynamic', mass: 100, hasBody: false}
VM101 about:srcdoc:560 [Space] Entity Entity_1 has no rigidbody component (normal for UI/effects)
VM101 about:srcdoc:568 [Space] Physics initialization complete: {totalEntities: 2, physicsEntities: 1, errors: 0}environment.

More details about all this are written in "The Book of The Future" and various other materials of ours, most of which are still poorly structured and not in English, but right now work is underway to create new detailed documentation, which will be presented in many languages.

## Inspiration

Our wonderful project, which will help create a global teknokomization and save humanity from final enslavement and total destruction, is currently in pre-alpha stage. We are implementing a React-based version of Universo Platformo that will serve as a foundation for creating interactive 3D/AR/VR experiences.

This implementation focuses on extending Flowise AI with UPDL (Universal Platform Description Language) to enable the creation of cross-platform 3D applications through a visual node-based interface.

## Where Am I and What Should I Do?

The near future, Omsk is the capital of the world, in the Olympus-1 tower, scientists explain to you that it is possible to connect your consciousness to a robot in another part of the Universe, in a parallel reality, controlled by robots we call Robocubans, through the recently discovered Great Ring system.

In Universo Platformo React, you are at the control panel of this revolutionary technology. Through the visual node editor, you can create interactive 3D scenes, AR experiences, and VR worlds that bridge our reality with parallel universes.

Your mission is to help build and expand this platform, creating new exporters, enhancing the node system, and contributing to the publication mechanism that will allow these experiences to be shared across the multiverse.

## Contact Information

For questions or collaboration, please contact:

-   VK: [https://vk.com/vladimirlevadnij](https://vk.com/vladimirlevadnij)
-   Telegram: [https://t.me/Vladimir_Levadnij](https://t.me/Vladimir_Levadnij)
-   Email: [<EMAIL>](mailto:<EMAIL>)

Our website: [https://universo.pro](https://universo.pro)

## Overview

Universo Platformo React is a project that extends Flowise AI with:

-   **Multi-user functionality** through Supabase integration
-   **Universal node system (UPDL)** for describing scenes and logic
-   **Multi-platform export** capabilities for generating AR/VR/3D applications
-   **Publishing mechanism** for deploying generated applications

The project aims to create a unified platform for developing interactive 3D applications that can be exported to various technologies including AR.js, PlayCanvas, Babylon.js, Three.js, and A-Frame.

## Current Status

**Current Sprint**: 0.23.0-alpha (January 2025)

**Primary Focus**:

-   APPs architecture implementation with apps/updl and apps/publish
-   Universal UPDL node system development
-   AR.js and PlayCanvas React exporters
-   Publication and export UI integration

## Tech Stack

-   Node.js (>=18.15.0 <19.0.0 || ^20)
-   PNPM (>=9)
-   React
-   Supabase (for multi-user functionality)
-   Flowise AI (core framework)

## Project Structure

```
universo-platformo-react/
├── packages/                  # Original Flowise packages
│   ├── components/            # Components and utilities
│   ├── server/                # Server-side code
│   └── ui/                    # Frontend
├── apps/                      # New APPs architecture
│   ├── updl/                  # UPDL node system
│   │   └── imp/               # Implementation
│   └── publish/               # Publication system
│       ├── imp/               # Implementation
│       │   ├── react/         # Frontend
│       │   │   └── miniapps/  # Technology-specific handlers
│       │   └── express/       # Backend
```

This structure allows for:

-   **Modularity**: Each functional area is contained within its own application
-   **Minimal Core Changes**: Original Flowise code remains largely untouched
-   **Easy Extension**: New technologies can be added as miniapps
-   **Clean Separation**: Clear boundaries between different functional areas

## Features

### UPDL Node System

The Universal Platform Description Language (UPDL) provides a unified way to describe 3D scenes and interactions:

-   **Scene Nodes**: Define the environment and root container
-   **Object Nodes**: 3D models, primitives with materials and transformations
-   **Camera Nodes**: Different camera types with configurable properties
-   **Light Nodes**: Various light types with color and intensity controls
-   **Interaction Nodes**: Handle user input and events
-   **Animation Nodes**: Control object animations and behaviors

### Multi-Platform Export

The system can export to multiple platforms from a single UPDL description:

-   **AR.js / A-Frame**: Web-based augmented reality
-   **PlayCanvas React**: React components for PlayCanvas engine
-   **Babylon.js**: Advanced 3D rendering
-   **Three.js**: Popular 3D library for web
-   **A-Frame VR**: Virtual reality experiences

### Publication System

Easily publish and share your creations:

-   **URL Structure**: Clean URLs for accessing published projects
-   **Embedding**: Options for embedded or standalone viewing
-   **Versioning**: Support for project revisions

## Universo Platformo Functionality

Universo Platformo is a universal platform for developing metaverses, virtual reality, multiplayer games, and industrial applications. It provides tools for creating, editing, and managing projects in real-time, and supports integration with various technology stacks.

Key functional areas include:

-   **Metaverses**: Tools for creating virtual worlds with unique ecosystems, including dynamic nature, social structures, and economic systems
-   **Game Development**: Visual scripting, AI for NPCs, physics editors, animation tools, and shader editors
-   **Networking**: Multiplayer support, real-time collaboration, and cross-platform compatibility
-   **Asset Management**: 3D model import/export, texture management, and asset optimization
-   **Industrial Integration**: CAD integration, digital twins, simulation tools, and IoT connectivity
-   **Project Management**: Team collaboration, version control, and task tracking
-   **High-Level Abstraction**: Export/import between different game engines and technology stacks

## Universo MMOOMM Functionality

Universo MMOOMM is a massive multiplayer online game built on Universo Platformo. It's similar to EVE Online and Star Citizen but with additional functionality that helps people unite, create organizations, and implement Kiberplano (Cyberplan) functionality to create production chains, develop products down to the smallest details, create common action plans, and bring their developments into the real world, including through various robots.

Key features include:

-   **Parallel Worlds**: Different worlds with unique economic systems (capitalist, socialist, etc.)
-   **Character Mechanics**: Character creation, development, FPS mechanics, and specialization
-   **Ship and Transport**: Ship management, customization, repair, and various ship types
-   **Careers and Professions**: Military, trading, resource gathering, research, and manufacturing
-   **Economy and Trade**: Dynamic economy, trade hubs, production, and contracts
-   **Social Mechanics**: Corporations, diplomacy, politics, and social interactions
-   **Exploration**: Scanning, planetary exploration, wormholes, and archaeological discoveries
-   **Base Building**: Construction of bases, orbital stations, and territorial control
-   **Science and Technology**: Research, technology trees, and technological breakthroughs

## Cross-Platform Implementation

Universo Platformo is being developed on multiple technology stacks:

-   **React**: This repository implements Universo Platformo on React and related technologies
-   **Godot**: A parallel implementation exists using the Godot game engine ([Universo Platformo Godot](https://github.com/teknokomo/universo-platformo-godot))
-   **PlayCanvas**: Another implementation using the PlayCanvas engine ([Universo Platformo Nebulo](https://github.com/teknokomo/universo-platformo-nebulo))
-   **Quasar**: A version built with the Quasar framework for cross-platform applications

Each implementation shares the same core concepts and goals while leveraging the strengths of its respective technology stack. The high-level abstraction layer allows projects to be exported between different implementations.

## Getting Started

### Prerequisites

-   Node.js (>=18.15.0 <19.0.0 || ^20)
-   PNPM (>=9)

### Installation

1. Clone the repository

    ```bash
    git clone https://github.com/teknokomo/universo-platformo-react.git
    cd universo-platformo-react
    ```

2. Install dependencies

    ```bash
    pnpm install
    ```

3. Set up environment variables

    - Create `.env` file in `packages/server` directory
    - Add required Supabase configuration:
        ```
        SUPABASE_URL=your_supabase_url
        SUPABASE_ANON_KEY=your_supabase_anon_key
        SUPABASE_JWT_SECRET=your_supabase_jwt_secret
        ```
    - Optionally, create `.env` file in `packages/ui` directory for UI-specific settings like `VITE_PORT`

    Note: After refactoring, Supabase configuration should only be specified in the `packages/server` directory.

4. Build the project

    ```bash
    pnpm build
    ```

5. Start the application

    ```bash
    pnpm start
    ```

6. Access the application at [http://localhost:3000](http://localhost:3000)

### Development Mode

For development with hot-reloading:

```bash
pnpm dev
```

This will start the application in development mode at [http://localhost:8080](http://localhost:8080)

## Roadmap

The development of Universo Platformo React follows a phased approach:

### Phase 1: Foundation

-   Establishing the APPs architecture
-   Implementing the core UPDL node system
-   Creating the first exporters for AR/VR technologies
-   Developing the publication system

### Phase 2: Expansion

-   Adding support for additional 3D technologies and platforms
-   Enhancing node functionality and user experience
-   Implementing advanced interaction capabilities
-   Expanding the multi-user functionality

### Phase 3: Integration

-   Connecting with robotic systems for Universo Kiberplano
-   Implementing resource management and planning tools
-   Creating digital twins for real-world environments
-   Developing comprehensive automation workflows

## Contributing

We welcome contributions to Universo Platformo React! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project uses dual licensing:

-   **Original Flowise Code (packages/ directory)**: Licensed under the [Apache License Version 2.0](LICENSE-Flowise.md)
-   **Universo Platformo Extensions (apps/ directory)**: Licensed under the Omsk Open License

The Omsk Open License is similar to the MIT license but includes additional "Basic Provisions" aimed at creating a meaningful and safe public domain while protecting traditional values.
